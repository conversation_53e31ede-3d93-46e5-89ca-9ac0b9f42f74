/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for multi-tenant
  experimental: {
    // Enable if using App Router with advanced features
    serverComponentsExternalPackages: ['@payloadcms/db-postgres'],
  },

  // Multi-tenant domain routing
  async rewrites() {
    return [
      // Admin routes - always go to admin
      {
        source: '/admin/:path*',
        destination: '/admin/:path*',
      },
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },

      // Tenant-specific routing based on hostname
      {
        source: '/((?!admin|api|_next|favicon.ico).*)',
        destination: '/sites/:tenant/:path*',
        has: [
          {
            type: 'host',
            value: '(?<tenant>.*)',
          },
        ],
      },
    ]
  },

  // Image optimization for multi-tenant
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname:
          process.env.S3_ENDPOINT?.replace('https://', '') ||
          'your-r2-domain.r2.cloudflarestorage.com',
        pathname: '/**',
      },
      // Add other image domains as needed
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
    ],
  },

  // Environment-specific configuration
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ]
  },

  // Redirects for SEO and UX
  async redirects() {
    return [
      // Redirect www to non-www for all domains
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'www.timeeting.com',
          },
        ],
        destination: 'https://timeeting.com/:path*',
        permanent: true,
      },
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'www.pynions.com',
          },
        ],
        destination: 'https://pynions.com/:path*',
        permanent: true,
      },
    ]
  },
}

module.exports = nextConfig
