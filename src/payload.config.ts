import { s3Storage } from '@payloadcms/storage-s3'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Posts } from './collections/Posts'
import { Pages } from './collections/Pages'
import { Authors } from './collections/Authors'
import { Categories } from './collections/Categories'
import { Tags } from './collections/Tags'
import { Tenants } from './collections/Tenants'
import { AdCampaigns } from './collections/AdCampaigns'
import { AdCreatives } from './collections/AdCreatives'
import { PricingRules } from './collections/PricingRules'
import { slugifyFilename } from './utils/slugify'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// Environment validation for production
if (!process.env.POSTGRES_URL || !process.env.PAYLOAD_SECRET) {
  throw new Error('Missing required environment variables: POSTGRES_URL and PAYLOAD_SECRET')
}
// Using vercelPostgresAdapter for Neon database (Vercel Postgres)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [
    Users,
    Media,
    Posts,
    Pages,
    Authors,
    Categories,
    Tags,
    Tenants,
    // Sponsorship Platform
    AdCampaigns,
    AdCreatives,
    PricingRules,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URL || '',
    },
    // Disable automatic schema pushing in development to avoid timeouts
    push: false,
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    multiTenantPlugin({
      collections: {
        posts: {},
        pages: {},
        authors: {},
        categories: {},
        tags: {},
        media: {
          // Allow media to be shared across sites
          useTenantAccess: false,
        },
      },
      // Function to determine if a user has access to all sites (super admin)
      userHasAccessToAllTenants: (user) => {
        return user?.roles?.includes('super-admin') || false
      },
      // Customize the site selector label
      tenantSelectorLabel: 'Select Site',

      // Enable debug mode to see site fields in admin
      debug: process.env.NODE_ENV === 'development',
    }),
    // Cloudflare R2 Storage Configuration - R2 ONLY, no local storage
    s3Storage({
      collections: {
        media: {
          // Dynamic prefix from database record will be used for tenant-specific folders
          // This allows tenant-specific folder structure: timeeting/filename.svg
          disableLocalStorage: true, // Explicitly disable local storage - files stored ONLY in R2
          generateFileURL: ({ filename, prefix }) => {
            // Generate clean, SEO-friendly URLs using tenant-specific domains
            let baseUrl = 'http://localhost:4000' // fallback for development

            // In production, map tenant prefix to their custom domains
            if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
              if (prefix === 'pynions') {
                baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
              } else if (prefix === 'timeeting') {
                baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
              } else if (prefix === 'justpricing') {
                baseUrl = process.env.NEXT_PUBLIC_JUSTPRICING_URL || 'https://justpricing.com'
              } else {
                // Fallback to admin domain for unknown tenants
                baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
              }
            } else {
              // Development: use localhost with tenant subdomains if available
              baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:4000'
            }

            // Return clean URL: domain.com/media/slugified-file-name.png
            return `${baseUrl}/media/${slugifyFilename(filename)}`
          },
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT || '',
        forcePathStyle: true, // Required for Cloudflare R2
        // Cloudflare R2 best practices for checksum compatibility
        requestChecksumCalculation: 'WHEN_REQUIRED',
        responseChecksumValidation: 'WHEN_REQUIRED',
      },
    }),
  ],
})
