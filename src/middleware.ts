import { NextRequest, NextResponse } from 'next/server'

// Domain to tenant slug mapping
const DOMAIN_TENANT_MAP: Record<string, string> = {
  // Development domains
  'localhost:4000': 'timeeting',
  'timeeting.localhost:4000': 'timeeting',
  'pynions.localhost:4000': 'pynions',
  'justpricing:localhost:4000': 'justpricing',

  // Production domains
  'timeeting.com': 'timeeting',
  'www.timeeting.com': 'timeeting',
  'pynions.com': 'pynions',
  'www.pynions.com': 'pynions',
  'justpricing.com': 'justpricing',
  'www.justpricing.com': 'justpricing',
}

export function middleware(request: NextRequest) {
  const hostname = request.headers.get('host') || ''
  const pathname = request.nextUrl.pathname

  // Skip middleware for admin, API, static files, and already tenant-specific routes
  if (
    pathname.startsWith('/admin') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/robots.txt') ||
    pathname.startsWith('/sitemap.xml') ||
    pathname.startsWith('/sites/')
  ) {
    return NextResponse.next()
  }

  // Get tenant slug from domain
  const tenantSlug = DOMAIN_TENANT_MAP[hostname] || 'timeeting'

  // Rewrite to tenant-specific route
  const url = request.nextUrl.clone()
  url.pathname = `/sites/${tenantSlug}${pathname}`

  console.log(`[Middleware] ${hostname}${pathname} → ${url.pathname}`)

  return NextResponse.rewrite(url)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
