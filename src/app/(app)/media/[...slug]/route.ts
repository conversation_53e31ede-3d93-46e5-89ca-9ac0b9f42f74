import { NextRequest, NextResponse } from 'next/server'
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3'
import { deSlugifyFilename } from '../../../../utils/slugify'

// Initialize S3 client for R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.S3_ENDPOINT,
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY_ID!,
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
  },
})

export async function GET(request: NextRequest, { params }: { params: { slug: string[] } }) {
  try {
    const filename = params.slug.join('/')
    console.log('🖼️ Media request for:', filename)

    // Get the host to determine which tenant this is for
    const host = request.headers.get('host') || ''
    console.log('🌐 Request host:', host)

    // Map host to tenant prefix
    let tenantPrefix = ''
    if (host.includes('pynions.com')) {
      tenantPrefix = 'pynions'
    } else if (host.includes('timeeting.com')) {
      tenantPrefix = 'timeeting'
    } else if (host.includes('justpricing.com')) {
      tenantPrefix = 'justpricing'
    } else {
      // For localhost or admin domain, try to determine from the filename or use a default
      // This is a fallback - in production, each tenant should have their own domain
      console.log('⚠️ Unknown host, using fallback logic')
      tenantPrefix = 'pynions' // Default fallback
    }

    console.log('🏢 Tenant prefix:', tenantPrefix)

    // Try to find the file in R2 storage
    // We need to try different variations because the filename might be stored differently
    const possibleKeys = [
      `${tenantPrefix}/${filename}`, // Direct match: pynions/cleanshot-2025-08-11-at-14-20-37-2x.png
      `${tenantPrefix}/${deSlugifyFilename(filename)}`, // De-slugified: pynions/cleanshot 2025 08 11 at 14 20 37 2x.png
    ]

    // Handle resized images (thumbnails, card, feature sizes)
    if (
      filename.includes('-300x300.webp') ||
      filename.includes('-640x480.webp') ||
      filename.includes('-1200x630.webp')
    ) {
      // This is a resized image generated by Payload
      // Extract the base filename and size
      const sizeMatch = filename.match(/-(\d+x\d+)\.webp$/)
      const size = sizeMatch ? sizeMatch[1] : '300x300'
      const baseFilename = filename.replace(/-\d+x\d+\.webp$/, '')

      console.log('🖼️ Looking for resized image:', { filename, baseFilename, size })

      // Try different variations for the resized image
      // Payload might store them with the original filename format
      const baseVariations = [
        baseFilename, // slugified base
        deSlugifyFilename(baseFilename), // de-slugified base
      ]

      for (const base of baseVariations) {
        // Try with different original extensions that Payload might have resized
        const originalExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.gif']
        for (const ext of originalExtensions) {
          // Try the resized version with original filename format
          possibleKeys.push(`${tenantPrefix}/${base}${ext.replace('.', `-${size}.webp`)}`)
          possibleKeys.push(`${tenantPrefix}/${base}-${size}.webp`)

          // Also try with spaces and special characters as Payload might store them
          const originalFormat = base.replace(/-/g, ' ').replace(/\s+/g, ' ')
          possibleKeys.push(`${tenantPrefix}/${originalFormat}${ext.replace('.', `-${size}.webp`)}`)
          possibleKeys.push(`${tenantPrefix}/${originalFormat}-${size}.webp`)
        }
      }

      // Also try to find the original file and serve it as fallback
      for (const base of baseVariations) {
        const originalExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg']
        for (const ext of originalExtensions) {
          possibleKeys.push(`${tenantPrefix}/${base}${ext}`)
          possibleKeys.push(`${tenantPrefix}/${deSlugifyFilename(base)}${ext}`)
        }
      }
    }

    console.log('🔍 Trying keys:', possibleKeys)

    let s3Object = null
    let usedKey = ''

    // Try each possible key until we find the file
    for (const key of possibleKeys) {
      try {
        console.log('🔍 Trying key:', key)
        const command = new GetObjectCommand({
          Bucket: process.env.S3_BUCKET!,
          Key: key,
        })

        s3Object = await s3Client.send(command)
        usedKey = key
        console.log('✅ Found file with key:', key)
        break
      } catch (error: any) {
        if (error.name === 'NoSuchKey') {
          console.log('❌ Key not found:', key)
          continue
        } else {
          console.error('❌ S3 error for key', key, ':', error)
          continue
        }
      }
    }

    if (!s3Object || !s3Object.Body) {
      console.log('❌ File not found in R2 storage')
      return new NextResponse('File not found', { status: 404 })
    }

    console.log('✅ File found, streaming response')

    // Convert the stream to a buffer
    const chunks: Uint8Array[] = []
    const reader = s3Object.Body.transformToWebStream().getReader()

    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      chunks.push(value)
    }

    const buffer = Buffer.concat(chunks)

    // Determine content type
    const contentType = s3Object.ContentType || 'application/octet-stream'

    // Set appropriate headers
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': buffer.length.toString(),
      'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
    })

    // Add CORS headers if needed
    headers.set('Access-Control-Allow-Origin', '*')
    headers.set('Access-Control-Allow-Methods', 'GET')

    return new NextResponse(buffer, {
      status: 200,
      headers,
    })
  } catch (error) {
    console.error('❌ Error serving media file:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}
