import React from 'react'
import { Inter } from 'next/font/google'
import { getCurrentSite, generateSiteThemeCSS } from '@/lib/site'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import './styles.css'

const inter = Inter({ subsets: ['latin'] })

export async function generateMetadata() {
  const site = await getCurrentSite()

  return {
    title: site?.seoDefaults?.title || site?.name || 'CraftPress',
    description: site?.seoDefaults?.description || 'A modern publishing platform',
    keywords: site?.seoDefaults?.keywords || '',
    openGraph: {
      title: site?.seoDefaults?.title || site?.name || 'CraftPress',
      description: site?.seoDefaults?.description || 'A modern publishing platform',
      images: site?.seoDefaults?.socialImage ? [site.seoDefaults.socialImage.url] : [],
    },
  }
}

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props
  const site = await getCurrentSite()

  // Default navigation - this could be made configurable per site
  const navigation = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' },
  ]

  // Default footer links - this could be made configurable per site
  const footerLinks = {
    Company: [
      { label: 'About', href: '/about' },
      { label: 'Contact', href: '/contact' },
    ],
    Legal: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' },
    ],
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {site && <style dangerouslySetInnerHTML={{ __html: generateSiteThemeCSS(site) }} />}
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen flex flex-col">
            {site && (
              <Header
                site={{
                  name: site.name,
                  slug: site.slug,
                  logo: site.themeConfig?.logo
                    ? {
                        url: site.themeConfig.logo.url,
                        alt: site.themeConfig.logo.alt || site.name,
                      }
                    : undefined,
                }}
                navigation={navigation}
              />
            )}

            <main className="flex-1">{children}</main>

            {site && (
              <Footer
                site={{
                  name: site.name,
                  slug: site.slug,
                }}
                links={footerLinks}
              />
            )}
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
