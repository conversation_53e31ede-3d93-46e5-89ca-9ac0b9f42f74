import type { CollectionConfig } from 'payload'
import { slugifyFilename } from '../utils/slugify'

export const Media: CollectionConfig = {
  slug: 'media',
  access: {
    read: () => true,
  },
  hooks: {
    beforeOperation: [
      async ({ req, operation, args }) => {
        if ((operation === 'create' || operation === 'update') && req.file) {
          console.log('🔍 Media beforeOperation hook triggered:', {
            operation,
            tenant: args.data?.tenant,
            fileName: req.file?.name,
          })

          // Get tenant ID from various sources
          const tenantId = args.data?.tenant

          if (tenantId) {
            let tenantSlug = ''

            // Get tenant slug from tenant data
            if (typeof tenantId === 'object' && tenantId.slug) {
              tenantSlug = tenantId.slug
            } else if (typeof tenantId === 'string' || typeof tenantId === 'number') {
              // If tenant is just an ID, we need to fetch the tenant to get the slug
              const payload = req.payload
              try {
                console.log('🔍 Fetching tenant by ID:', tenantId)
                const tenant = await payload.findByID({
                  collection: 'tenants',
                  id: tenantId.toString(),
                })
                console.log('🏢 Tenant found:', tenant)
                tenantSlug = tenant.slug || tenantId.toString()
              } catch (error) {
                console.log('❌ Error fetching tenant:', error)
                tenantSlug = tenantId.toString()
              }
            }

            console.log('🏢 Tenant slug resolved:', tenantSlug)

            if (tenantSlug) {
              // Set the prefix for S3 storage - this creates the folder structure
              // The S3 adapter will automatically join prefix + filename with "/"
              args.data.prefix = tenantSlug
              console.log('📁 Set S3 prefix to:', tenantSlug)
              console.log('📁 S3 adapter will store file as:', `${tenantSlug}/${req.file?.name}`)
            }
          }
        }

        return args
      },
    ],
    afterRead: [
      ({ doc, req }) => {
        // Fix thumbnailURL and all size URLs to use tenant-specific domain and correct filenames
        if (doc.prefix) {
          let baseUrl = 'http://localhost:4000' // fallback

          // Map tenant prefix to their custom domains
          if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
            if (doc.prefix === 'pynions') {
              baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
            } else if (doc.prefix === 'timeeting') {
              baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
            } else if (doc.prefix === 'justpricing') {
              baseUrl = process.env.NEXT_PUBLIC_JUSTPRICING_URL || 'https://justpricing.com'
            } else {
              baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
            }
          } else {
            baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:4000'
          }

          // Fix thumbnailURL if it exists
          if (doc.thumbnailURL) {
            // Extract the filename from the URL (handle both relative and absolute URLs)
            let filename = ''
            if (doc.thumbnailURL.startsWith('/')) {
              // Relative URL: /api/media/file/filename
              filename = doc.thumbnailURL.replace('/api/media/file/', '')
            } else {
              // Absolute URL: extract filename from the end
              const urlParts = doc.thumbnailURL.split('/')
              filename = urlParts[urlParts.length - 1]
            }

            // Generate correct thumbnail filename based on original filename
            if (doc.filename) {
              const originalSlugified = slugifyFilename(doc.filename)
              const extension = originalSlugified.split('.').pop()
              const nameWithoutExt = originalSlugified.replace(`.${extension}`, '')

              // Create proper thumbnail filename: original-name-300x300.webp
              const thumbnailFilename = `${nameWithoutExt}-300x300.webp`
              doc.thumbnailURL = `${baseUrl}/media/${thumbnailFilename}`
            } else {
              // Fallback: use the extracted filename as-is but slugify it
              doc.thumbnailURL = `${baseUrl}/media/${slugifyFilename(filename)}`
            }
          }

          // Fix all size URLs in the sizes object
          if (doc.sizes && typeof doc.sizes === 'object') {
            Object.keys(doc.sizes).forEach((sizeName) => {
              const sizeData = doc.sizes[sizeName]
              if (sizeData && sizeData.url && doc.filename) {
                const originalSlugified = slugifyFilename(doc.filename)
                const extension = originalSlugified.split('.').pop()
                const nameWithoutExt = originalSlugified.replace(`.${extension}`, '')

                // Create proper size filename: original-name-WIDTHxHEIGHT.webp
                const sizeFilename = `${nameWithoutExt}-${sizeData.width}x${sizeData.height}.webp`
                sizeData.url = `${baseUrl}/media/${sizeFilename}`
              }
            })
          }
        }
        return doc
      },
    ],
  },
  fields: [
    {
      name: 'prefix',
      type: 'text',
      admin: {
        hidden: true,
        readOnly: true,
      },
    },
    {
      name: 'alt',
      type: 'text',
      required: true,
      admin: {
        description: 'Alternative text for accessibility and SEO',
      },
    },
    {
      name: 'caption',
      type: 'text',
      admin: {
        description: 'Optional caption for the media',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Detailed description of the media content',
      },
    },
    {
      name: 'usage',
      type: 'select',
      options: [
        { label: 'General', value: 'general' },
        { label: 'Featured Image', value: 'featured' },
        { label: 'Logo', value: 'logo' },
        { label: 'Banner/Header', value: 'banner' },
        { label: 'Advertisement', value: 'ad' },
        { label: 'Social Media', value: 'social' },
      ],
      defaultValue: 'general',
      admin: {
        description: 'How this media is intended to be used',
      },
    },
    {
      name: 'tags',
      type: 'text',
      hasMany: true,
      admin: {
        description: 'Tags for organizing media (comma-separated)',
      },
    },
  ],
  upload: {
    // Image optimization settings
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300,
        height: 300,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 80,
          },
        },
      },
      {
        name: 'card',
        width: 640,
        height: 480,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 85,
          },
        },
      },
      {
        name: 'feature',
        width: 1200,
        height: 630,
        position: 'centre',
        formatOptions: {
          format: 'webp',
          options: {
            quality: 90,
          },
        },
      },
    ],
    // File validation
    mimeTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'image/svg+xml',
      'application/pdf',
      'video/mp4',
      'video/webm',
    ],
    adminThumbnail: 'thumbnail',
  },
  admin: {
    useAsTitle: 'alt',
    defaultColumns: ['alt', 'usage', 'updatedAt'],
    description: 'Manage media files with automatic optimization and tenant organization',
  },
}
