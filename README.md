# Ship - Multi-Tenant CMS

**A modern, scalable multi-tenant content management system built with Payload CMS 3.0 and Next.js 15.**

Ship enables you to manage multiple websites from a single admin panel, with complete tenant isolation, custom domains, and a revolutionary self-serve advertising platform.

## 🚀 **Live Production Sites**

- **Admin Panel**: [admin.craftled.com/admin](https://admin.craftled.com/admin)
- **Timeeting**: [timeeting.com](https://timeeting.com) - Time Management & Productivity
- **Pynions**: [pynions.com](https://pynions.com) - Python Insights & Opinions

## ✨ **Features**

- 🏢 **Multi-Tenant Architecture** - Manage multiple sites from one admin panel
- 🌐 **Custom Domains** - Each site can have its own domain and branding
- 🎨 **Tenant-Specific Theming** - Custom colors, logos, and SEO settings per site
- 📱 **Responsive Design** - Modern, mobile-first frontend with Tailwind CSS
- 🔒 **Tenant Isolation** - Complete data separation between sites
- 💾 **PostgreSQL Database** - Powered by Ver<PERSON> (Neon)
- ☁️ **Cloudflare R2 Storage** - Scalable media storage with tenant-specific folders
- 🎯 **Self-Serve Advertising** - Revolutionary sponsorship platform
- 🔍 **SEO Optimized** - Automatic sitemaps, RSS feeds, and meta tags
- ⚡ **Performance** - Built on Next.js 15 with App Router

## 🛠 **Tech Stack**

- **Next.js 15** - React framework with App Router
- **Payload CMS 3.0** - Headless CMS with multi-tenant support
- **PostgreSQL** - Vercel Postgres (Neon) database
- **Cloudflare R2** - Media storage
- **Tailwind CSS v4** - Styling framework
- **TypeScript** - Type safety
- **Vercel** - Hosting and deployment

## 🚀 **Quick Start - Local Development**

### Prerequisites

- Node.js 18+ and pnpm
- PostgreSQL database (or use Vercel Postgres)
- Cloudflare R2 bucket (optional, for media storage)

### Setup

1. **Clone and Install**

   ```bash
   git clone https://github.com/craftled/ship.git
   cd ship
   pnpm install
   ```

2. **Environment Variables**

   ```bash
   cp .env.example .env.local
   ```

   Configure your `.env.local` with:

   ```env
   # Database (Vercel Postgres - Neon)
   DATABASE_URL=**********************************************************
   POSTGRES_URL=**********************************************************

   # Payload CMS
   PAYLOAD_SECRET=your-secret-key-here
   PAYLOAD_PUBLIC_SERVER_URL=http://localhost:4000

   # Cloudflare R2 Storage (optional)
   S3_ENDPOINT=https://[account-id].r2.cloudflarestorage.com
   S3_REGION=auto
   S3_BUCKET=your-bucket-name
   S3_ACCESS_KEY_ID=your-access-key
   S3_SECRET_ACCESS_KEY=your-secret-key
   ```

3. **Start Development Server**

   ```bash
   pnpm dev
   ```

   Open [http://localhost:4000](http://localhost:4000) and create your first admin user.

4. **Setup Local Domains (Optional)**

   ```bash
   chmod +x scripts/setup-local-domains.sh
   ./scripts/setup-local-domains.sh
   ```

   This enables:
   - `http://localhost:4000` (default - Timeeting)
   - `http://timeeting.localhost:4000` (Timeeting)
   - `http://pynions.localhost:4000` (Pynions)

## 🏢 **Adding a New Domain/Site Tenant**

Follow these steps to add a new website to your Ship installation:

### Step 1: Create Tenant in Admin Panel

1. **Access Admin Panel**
   - Production: [admin.craftled.com/admin](https://admin.craftled.com/admin)
   - Local: [localhost:4000/admin](http://localhost:4000/admin)

2. **Navigate to Sites**
   - Click "Sites" in the sidebar
   - Click "Create new Site"

3. **Fill Tenant Information**

   ```
   Site Name: Your Site Name
   Site Slug: your-site-slug
   Primary Domain: yoursite.com
   Active Site: ✓ (checked)
   ```

4. **Configure Theme (Optional)**

   ```
   Primary Color: #3B82F6 (hex color)
   Site Logo: Upload or select existing
   Favicon: Upload or select existing
   ```

5. **Set SEO Defaults**

   ```
   Default Site Title: Your Site - Tagline
   Default Meta Description: Your site description...
   Default Keywords: keyword1, keyword2, keyword3
   Default Social Share Image: Upload image
   ```

6. **Save** the tenant

### Step 2: Configure Domain Routing

#### For Production (Vercel):

1. **Add Domain in Vercel Dashboard**
   - Go to your Vercel project settings
   - Add `yoursite.com` as a custom domain
   - Configure DNS records as instructed

2. **Update Middleware (if needed)**

   Edit `src/middleware.ts` and add your domain:

   ```typescript
   const DOMAIN_TENANT_MAP: Record<string, string> = {
     // Existing domains
     'timeeting.com': 'timeeting',
     'pynions.com': 'pynions',

     // Add your new domain
     'yoursite.com': 'your-site-slug',
     'www.yoursite.com': 'your-site-slug',
   }
   ```

3. **Update Production Config**

   Edit `payload.config.production.ts` CORS and CSRF arrays:

   ```typescript
   cors: [
     'https://timeeting.com',
     'https://pynions.com',
     'https://yoursite.com', // Add your domain
     'https://admin.craftled.com',
     // ...
   ],
   csrf: [
     'https://timeeting.com',
     'https://pynions.com',
     'https://yoursite.com', // Add your domain
     'https://admin.craftled.com',
     // ...
   ],
   ```

4. **Update Media URL Generation**

   Edit `payload.config.production.ts` in the `generateFileURL` function:

   ```typescript
   // Map tenant prefix to their custom domains
   if (prefix === 'pynions') {
     baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
   } else if (prefix === 'timeeting') {
     baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
   } else if (prefix === 'your-site-slug') {
     baseUrl = process.env.NEXT_PUBLIC_YOURSITE_URL || 'https://yoursite.com'
   } else {
     // Fallback to admin domain for unknown tenants
     baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
   }
   ```

5. **Update Development Config**

   Edit `src/payload.config.ts` in the `generateFileURL` function:

   ```typescript
   // In production, map tenant prefix to their custom domains
   if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
     if (prefix === 'pynions') {
       baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
     } else if (prefix === 'timeeting') {
       baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
     } else if (prefix === 'your-site-slug') {
       baseUrl = process.env.NEXT_PUBLIC_YOURSITE_URL || 'https://yoursite.com'
     } else {
       // Fallback to admin domain for unknown tenants
       baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
     }
   }
   ```

6. **Update Media Collection Hook**

   Edit `src/collections/Media.ts` in the `afterRead` hook:

   ```typescript
   // Map tenant prefix to their custom domains
   if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
     if (doc.prefix === 'pynions') {
       baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
     } else if (doc.prefix === 'timeeting') {
       baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
     } else if (doc.prefix === 'your-site-slug') {
       baseUrl = process.env.NEXT_PUBLIC_YOURSITE_URL || 'https://yoursite.com'
     } else {
       baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
     }
   }
   ```

7. **Add Environment Variable (Optional)**

   Add to your `.env.local` for consistency:

   ```env
   NEXT_PUBLIC_YOURSITE_URL="https://yoursite.com"
   ```

#### For Local Development:

1. **Add to Local Domain Map**

   Edit `src/middleware.ts`:

   ```typescript
   const DOMAIN_TENANT_MAP: Record<string, string> = {
     // Development domains
     'localhost:4000': 'timeeting',
     'timeeting.localhost:4000': 'timeeting',
     'pynions.localhost:4000': 'pynions',
     'yoursite.localhost:4000': 'your-site-slug', // Add this

     // Production domains...
   }
   ```

2. **Update Local Hosts File**

   Add to `/etc/hosts`:

   ```
   127.0.0.1 yoursite.localhost
   ```

### Step 3: Deploy Changes

1. **Commit and Push**

   ```bash
   git add .
   git commit -m "feat: add yoursite.com tenant"
   git push origin main
   ```

2. **Verify Deployment**
   - Check Vercel deployment completes successfully
   - Test the new domain: `https://yoursite.com`
   - Verify admin panel shows the new site in the site selector

### Step 4: Add Content

1. **Switch to New Site**
   - In admin panel, use the site selector to switch to your new site
   - Create authors, categories, and tags
   - Add posts and pages

2. **Customize Branding**
   - Upload site logo and favicon
   - Adjust theme colors
   - Configure SEO settings

### Step 5: Test Multi-Tenant Functionality

- ✅ **Domain Routing**: `https://yoursite.com` shows correct content
- ✅ **Admin Access**: Site appears in admin site selector
- ✅ **Content Isolation**: Content only appears on the correct site
- ✅ **SEO Assets**: Check `/sitemap.xml`, `/robots.txt`, `/feed.xml`
- ✅ **Media Storage**: Uploads go to tenant-specific folders

## 📁 **Project Structure**

```
ship/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (frontend)/         # Frontend pages
│   │   ├── admin/              # Payload admin
│   │   ├── api/                # API routes
│   │   └── sites/[tenant]/     # Multi-tenant routes
│   ├── collections/            # Payload collections
│   ├── components/             # React components
│   ├── lib/                    # Utilities
│   └── middleware.ts           # Domain routing
├── scripts/                    # Utility scripts
├── payload.config.ts           # Payload configuration
└── payload.config.production.ts # Production config
```

## 🗂 **Collections**

### Core Collections

- **Sites (Tenants)** - Multi-tenant site configuration
- **Users** - Admin users with role-based access
- **Media** - File uploads with tenant-specific folders

### Content Collections (Per Tenant)

- **Posts** - Blog posts and articles
- **Pages** - Static pages
- **Authors** - Content authors and contributors
- **Categories** - Content categorization
- **Tags** - Content tagging system

### Sponsorship Platform

- **Ad Campaigns** - Advertising campaigns
- **Ad Creatives** - Advertisement assets
- **Pricing Rules** - Dynamic pricing configuration

## 🔧 **Configuration**

### Environment Variables

See `vercel-env-variables.md` for complete environment variable documentation.

**Required:**

```env
DATABASE_URL=postgres://...          # Vercel Postgres connection
PAYLOAD_SECRET=your-secret-key       # JWT signing secret
```

**Optional:**

```env
S3_ENDPOINT=https://...              # Cloudflare R2 endpoint
S3_BUCKET=your-bucket               # R2 bucket name
S3_ACCESS_KEY_ID=your-key           # R2 access key
S3_SECRET_ACCESS_KEY=your-secret    # R2 secret key
```

### Multi-Tenant Configuration

The multi-tenant system uses:

1. **Domain-based routing** via `src/middleware.ts`
2. **Tenant isolation** via Payload's multi-tenant plugin
3. **Dynamic content** based on the current tenant context

## 🚀 **Deployment**

### Vercel (Recommended)

1. **Connect Repository**
   - Import project to Vercel
   - Connect to GitHub repository

2. **Configure Environment Variables**
   - Add all required environment variables
   - Use Vercel Postgres for database
   - Configure Cloudflare R2 for media storage

3. **Add Custom Domains**
   - Add each tenant domain in Vercel dashboard
   - Configure DNS records as instructed

4. **Deploy**
   ```bash
   git push origin main
   ```

### Manual Deployment

```bash
# Build for production
pnpm build

# Start production server
pnpm start
```

## 📊 **Data Migration**

### Export Data (Before Migration)

```bash
node scripts/export-data.js
```

### Import Data (After Migration)

```bash
node scripts/import-data.js
```

### Seed Test Content

```bash
pnpm tsx scripts/seed-test-content.ts
```

## 🔍 **SEO Features**

Each tenant automatically gets:

- **Dynamic Sitemaps**: `/sitemap.xml`
- **RSS Feeds**: `/feed.xml`
- **Robots.txt**: `/robots.txt`
- **Open Graph Images**: `/og` (auto-generated)
- **Structured Data**: JSON-LD for articles and organization
- **Meta Tags**: Dynamic title, description, and keywords

## 🎨 **Theming**

Each tenant can customize:

- **Primary Color**: Hex color code
- **Logo**: Custom logo upload
- **Favicon**: Custom favicon
- **SEO Defaults**: Title, description, keywords
- **Social Share Image**: Default OG image

## 🛡 **Security**

- **CORS Protection**: Domain-specific CORS configuration
- **CSRF Protection**: Cross-site request forgery protection
- **Environment Isolation**: Separate configs for dev/production
- **Tenant Isolation**: Complete data separation between sites
- **Role-based Access**: Admin, editor, and author roles

## 📈 **Performance**

- **Static Generation**: ISR for dynamic content
- **Image Optimization**: Next.js Image component
- **CDN**: Cloudflare R2 with global distribution
- **Database**: Connection pooling with Neon
- **Caching**: Built-in Next.js caching strategies

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: See `PRD.md` for detailed project requirements
- **Environment Setup**: See `vercel-env-variables.md`
- **Issues**: Open a GitHub issue
- **Discussions**: Start a GitHub discussion

## 🎯 **Roadmap**

- [ ] Umami Analytics Integration
- [ ] Advanced Sponsorship Analytics
- [ ] Multi-language Support
- [ ] Advanced Theme Customization
- [ ] Webhook Integration
- [ ] API Rate Limiting
- [ ] Advanced User Roles

---

**Built with ❤️ using Payload CMS 3.0 and Next.js 15**
