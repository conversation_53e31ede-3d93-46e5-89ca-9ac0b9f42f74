// payload.config.ts - Production configuration
import path from 'path'
import { buildConfig } from 'payload'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { s3Storage } from '@payloadcms/storage-s3'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'

// Import your collections
import { Users } from './src/collections/Users'
import { Tenants } from './src/collections/Tenants'
import { Posts } from './src/collections/Posts'
import { Media } from './src/collections/Media'
import { Categories } from './src/collections/Categories'
import { Authors } from './src/collections/Authors'
import { Tags } from './src/collections/Tags'
import { Pages } from './src/collections/Pages'
import { AdCampaigns } from './src/collections/AdCampaigns'
import { AdCreatives } from './src/collections/AdCreatives'
import { PricingRules } from './src/collections/PricingRules'
import { slugifyFilename } from './src/utils/slugify'

export default buildConfig({
  // Production server URL
  serverURL: process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : process.env.NEXT_PUBLIC_ADMIN_URL || 'http://localhost:3000',

  // Secret for JWT signing
  secret: process.env.PAYLOAD_SECRET!,

  // Database configuration
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URL!,
    },
    // Production optimizations
    prodMigrations: process.env.NODE_ENV === 'production' ? undefined : undefined,
  }),

  // Admin configuration
  admin: {
    meta: {
      titleSuffix: '- CraftPress Admin',
    },
  },

  // CORS for production
  cors: [
    'https://timeeting.com',
    'https://pynions.com',
    'https://admin.craftled.com',
    'https://justpricing.com',
    ...(process.env.VERCEL_URL ? [`https://${process.env.VERCEL_URL}`] : []),
  ],

  // CSRF protection
  csrf: [
    'https://timeeting.com',
    'https://pynions.com',
    'https://admin.craftled.com',
    'https://justpricing.com',
    ...(process.env.VERCEL_URL ? [`https://${process.env.VERCEL_URL}`] : []),
  ],

  // Collections
  collections: [
    Users,
    Tenants,
    Posts,
    Media,
    Categories,
    Authors,
    Tags,
    Pages,
    AdCampaigns,
    AdCreatives,
    PricingRules,
  ],

  // Plugins
  plugins: [
    // Multi-tenant plugin
    multiTenantPlugin({
      collections: {
        posts: {},
        media: {},
        categories: {},
        authors: {},
        tags: {},
        pages: {},
        'ad-campaigns': {},
        'ad-creatives': {},
        'pricing-rules': {},
        // Global collections
        tenants: {
          isGlobal: true,
        },
      },
      tenantsSlug: 'tenants',
    }),

    // S3/R2 Storage plugin
    s3Storage({
      collections: {
        media: {
          // Dynamic prefix from database record will be used for tenant-specific folders
          // This allows tenant-specific folder structure: timeeting/filename.svg
          disableLocalStorage: true, // Explicitly disable local storage - files stored ONLY in R2
          generateFileURL: ({ filename, prefix }) => {
            // Generate clean, SEO-friendly URLs using tenant-specific domains
            let baseUrl = 'http://localhost:4000' // fallback

            // Map tenant prefix to their custom domains
            if (prefix === 'pynions') {
              baseUrl = process.env.NEXT_PUBLIC_PYNIONS_URL || 'https://pynions.com'
            } else if (prefix === 'timeeting') {
              baseUrl = process.env.NEXT_PUBLIC_TIMEETING_URL || 'https://timeeting.com'
            } else if (prefix === 'justpricing') {
              baseUrl = process.env.NEXT_PUBLIC_JUSTPRICING_URL || 'https://justpricing.com'
            } else {
              // Fallback to admin domain for unknown tenants
              baseUrl = process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.craftled.com'
            }

            // Return clean URL: domain.com/media/slugified-file-name.png
            return `${baseUrl}/media/${slugifyFilename(filename)}`
          },
        },
      },
      bucket: process.env.S3_BUCKET!,
      config: {
        endpoint: process.env.S3_ENDPOINT!,
        region: process.env.S3_REGION!,
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID!,
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
        },
        forcePathStyle: true,
      },
    }),
  ],

  // TypeScript configuration
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },

  // Production optimizations
  graphQL: {
    schemaOutputFile: path.resolve(__dirname, 'generated-schema.graphql'),
  },

  // Rate limiting for production (disabled for now)
  // rateLimit: {
  //   max: 2000,
  //   trustProxy: true,
  // },

  // Email configuration (configure when needed)
  // email: resendAdapter({
  //   defaultFromAddress: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
  //   defaultFromName: 'CraftPress',
  //   apiKey: process.env.RESEND_API_KEY || '',
  // }),
})
