#!/bin/bash

# Setup script for local domain testing
# This script adds localhost domain entries for multi-tenant testing

echo "🚀 Setting up local domains for multi-tenant testing..."

# Check if running on macOS or Linux
if [[ "$OSTYPE" == "darwin"* ]]; then
    HOSTS_FILE="/etc/hosts"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    HOSTS_FILE="/etc/hosts"
else
    echo "❌ Unsupported operating system. Please manually add the following entries to your hosts file:"
    echo "127.0.0.1 timeeting.localhost"
    echo "127.0.0.1 pynions.localhost"
    exit 1
fi

# Backup hosts file
echo "📋 Creating backup of hosts file..."
sudo cp $HOSTS_FILE $HOSTS_FILE.backup.$(date +%Y%m%d_%H%M%S)

# Check if entries already exist
if grep -q "timeeting.localhost" $HOSTS_FILE && grep -q "pynions.localhost" $HOSTS_FILE; then
    echo "✅ Local domains already configured!"
    echo ""
    echo "🌐 Available domains:"
    echo "  - http://localhost:4000 (Timeeting - default)"
    echo "  - http://timeeting.localhost:4000 (Timeeting)"
    echo "  - http://pynions.localhost:4000 (Pynions)"
    echo ""
    echo "🚀 Start the development server with: pnpm dev"
    exit 0
fi

# Add entries to hosts file
echo "📝 Adding local domain entries to $HOSTS_FILE..."

# Create temporary file with new entries
cat << EOF > /tmp/craftpress_hosts
# CraftPress local development domains
127.0.0.1 timeeting.localhost
127.0.0.1 pynions.localhost
EOF

# Add entries to hosts file
sudo sh -c "cat /tmp/craftpress_hosts >> $HOSTS_FILE"

# Clean up temporary file
rm /tmp/craftpress_hosts

echo "✅ Local domains configured successfully!"
echo ""
echo "🌐 Available domains:"
echo "  - http://localhost:4000 (Timeeting - default)"
echo "  - http://timeeting.localhost:4000 (Timeeting)"
echo "  - http://pynions.localhost:4000 (Pynions)"
echo ""
echo "📝 Note: You may need to clear your browser cache or use incognito mode"
echo "🚀 Start the development server with: pnpm dev"
echo ""
echo "🔧 To remove these entries later, restore from backup:"
echo "   sudo cp $HOSTS_FILE.backup.* $HOSTS_FILE"
