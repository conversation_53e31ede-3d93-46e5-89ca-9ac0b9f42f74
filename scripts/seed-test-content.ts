import { getPayload } from 'payload'
import config from '@payload-config'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

console.log('Environment check:')
console.log('PAYLOAD_SECRET exists:', !!process.env.PAYLOAD_SECRET)
console.log('POSTGRES_URL exists:', !!process.env.POSTGRES_URL)

async function seedTestContent() {
  try {
    console.log('🌱 Seeding test content for multi-tenant setup...')

    const payloadConfig = await config
    const payload = await getPayload({ config: payloadConfig })

    // Create test tenants
    console.log('📋 Creating test tenants...')

    const tenants = [
      {
        name: 'Timeeting',
        slug: 'timeeting',
        domain: 'localhost:4000',
        isActive: true,
        seoDefaults: {
          title: 'Timeeting - Time Management Solutions',
          description:
            'Discover the best time management tools and techniques to boost your productivity.',
        },
      },
      {
        name: 'Pyn<PERSON>',
        slug: 'pynions',
        domain: 'pynions.localhost:4000',
        isActive: true,
        seoDefaults: {
          title: 'Pynions - Tech Opinions & Reviews',
          description:
            'Honest opinions and in-depth reviews of the latest technology products and services.',
        },
      },
    ]

    const createdTenants = []
    for (const tenantData of tenants) {
      try {
        // Check if tenant already exists
        const existing = await payload.find({
          collection: 'tenants',
          where: { slug: { equals: tenantData.slug } },
          limit: 1,
        })

        if (existing.docs.length > 0) {
          console.log(`✅ Tenant "${tenantData.name}" already exists`)
          createdTenants.push(existing.docs[0])
        } else {
          const tenant = await payload.create({
            collection: 'tenants',
            data: tenantData,
          })
          console.log(`✅ Created tenant: ${tenant.name}`)
          createdTenants.push(tenant)
        }
      } catch (error) {
        console.error(`❌ Error creating tenant ${tenantData.name}:`, error)
      }
    }

    // Create categories for each tenant
    console.log('📂 Creating categories...')

    const categoryData = [
      { name: 'Technology', slug: 'technology', description: 'Latest tech news and trends' },
      {
        name: 'Productivity',
        slug: 'productivity',
        description: 'Tips and tools for better productivity',
      },
      { name: 'Reviews', slug: 'reviews', description: 'Product and service reviews' },
      { name: 'Tutorials', slug: 'tutorials', description: 'How-to guides and tutorials' },
    ]

    const createdCategories = {}
    for (const tenant of createdTenants) {
      createdCategories[tenant.id] = []

      for (const catData of categoryData) {
        try {
          const category = await payload.create({
            collection: 'categories',
            data: {
              ...catData,
              tenant: tenant.id,
            },
          })
          createdCategories[tenant.id].push(category)
          console.log(`✅ Created category "${category.name}" for ${tenant.name}`)
        } catch (error) {
          console.error(`❌ Error creating category ${catData.name} for ${tenant.name}:`, error)
        }
      }
    }

    // Create tags for each tenant
    console.log('🏷️ Creating tags...')

    const tagData = [
      { name: 'JavaScript', slug: 'javascript' },
      { name: 'React', slug: 'react' },
      { name: 'Next.js', slug: 'nextjs' },
      { name: 'AI', slug: 'ai' },
      { name: 'SaaS', slug: 'saas' },
      { name: 'Startup', slug: 'startup' },
    ]

    const createdTags = {}
    for (const tenant of createdTenants) {
      createdTags[tenant.id] = []

      for (const tagData of tagData) {
        try {
          const tag = await payload.create({
            collection: 'tags',
            data: {
              ...tagData,
              tenant: tenant.id,
            },
          })
          createdTags[tenant.id].push(tag)
          console.log(`✅ Created tag "${tag.name}" for ${tenant.name}`)
        } catch (error) {
          console.error(`❌ Error creating tag ${tagData.name} for ${tenant.name}:`, error)
        }
      }
    }

    // Create test posts for each tenant
    console.log('📝 Creating test posts...')

    const postTemplates = [
      {
        title: 'Getting Started with Next.js 14',
        slug: 'getting-started-nextjs-14',
        excerpt:
          'Learn how to build modern web applications with Next.js 14 and its latest features.',
        content: [
          {
            children: [
              {
                text: 'Next.js 14 brings exciting new features and improvements to the React framework.',
              },
            ],
          },
        ],
        status: 'published',
        publishedAt: new Date().toISOString(),
      },
      {
        title: 'The Future of AI in Web Development',
        slug: 'future-ai-web-development',
        excerpt:
          'Exploring how artificial intelligence is transforming the way we build web applications.',
        content: [
          {
            children: [
              {
                text: 'AI is revolutionizing web development with automated code generation and intelligent debugging.',
              },
            ],
          },
        ],
        status: 'published',
        publishedAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      },
      {
        title: 'Building Scalable SaaS Applications',
        slug: 'building-scalable-saas-applications',
        excerpt:
          'Best practices for creating SaaS applications that can scale to millions of users.',
        content: [
          {
            children: [
              {
                text: 'Scalability is crucial for SaaS success. Here are the key principles to follow.',
              },
            ],
          },
        ],
        status: 'published',
        publishedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      },
    ]

    for (const tenant of createdTenants) {
      for (let i = 0; i < postTemplates.length; i++) {
        const template = postTemplates[i]
        const categories = createdCategories[tenant.id] || []
        const tags = createdTags[tenant.id] || []

        try {
          const post = await payload.create({
            collection: 'posts',
            data: {
              ...template,
              slug: `${template.slug}-${tenant.slug}`,
              title: `${template.title} - ${tenant.name}`,
              tenant: tenant.id,
              categories: categories.slice(0, 2).map((cat) => cat.id), // First 2 categories
              tags: tags.slice(i, i + 2).map((tag) => tag.id), // 2 tags per post
            },
          })
          console.log(`✅ Created post "${post.title}"`)
        } catch (error) {
          console.error(`❌ Error creating post for ${tenant.name}:`, error)
        }
      }
    }

    // Create test pages for each tenant
    console.log('📄 Creating test pages...')

    const pageTemplates = [
      {
        title: 'About Us',
        slug: 'about',
        excerpt: 'Learn more about our mission and team.',
        content: [
          {
            children: [
              { text: 'We are passionate about delivering high-quality content and solutions.' },
            ],
          },
        ],
        status: 'published',
        publishedAt: new Date().toISOString(),
      },
      {
        title: 'Contact',
        slug: 'contact',
        excerpt: 'Get in touch with our team.',
        content: [
          {
            children: [{ text: 'Contact us for any questions or collaboration opportunities.' }],
          },
        ],
        status: 'published',
        publishedAt: new Date().toISOString(),
      },
    ]

    for (const tenant of createdTenants) {
      for (const template of pageTemplates) {
        try {
          const page = await payload.create({
            collection: 'pages',
            data: {
              ...template,
              tenant: tenant.id,
            },
          })
          console.log(`✅ Created page "${page.title}" for ${tenant.name}`)
        } catch (error) {
          console.error(`❌ Error creating page for ${tenant.name}:`, error)
        }
      }
    }

    console.log('🎉 Test content seeding completed!')
    console.log('')
    console.log('🌐 Test your multi-tenant setup:')
    console.log('  - http://localhost:4000 (Timeeting - default)')
    console.log('  - http://timeeting.localhost:4000 (Timeeting)')
    console.log('  - http://pynions.localhost:4000 (Pynions)')
    console.log('')
    console.log('📝 Test URLs:')
    console.log('  - /about (page)')
    console.log('  - /contact (page)')
    console.log('  - /getting-started-nextjs-14-timeeting (post)')
    console.log('  - /category/technology (category archive)')
    console.log('  - /tag/javascript (tag archive)')
  } catch (error) {
    console.error('❌ Error seeding test content:', error)
  }
}

// Run the seeding function
seedTestContent()
  .then(() => {
    console.log('✅ Seeding script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Seeding script failed:', error)
    process.exit(1)
  })
