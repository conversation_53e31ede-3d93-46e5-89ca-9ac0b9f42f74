#!/usr/bin/env node

/**
 * Export data from current database before migration
 * Run with: node scripts/export-data.js
 */

import fs from 'fs'
import path from 'path'
import { config } from 'dotenv'

async function exportData() {
  try {
    console.log('🔄 Starting data export from current database...')

    // Import Payload after environment is loaded
    config({ path: '.env.local' })

    const { getPayload } = await import('payload')
    const payloadConfig = await import('../src/payload.config.ts')

    const payload = await getPayload({ config: payloadConfig.default })

    const exportData = {}

    // Export all collections
    const collections = [
      'tenants',
      'posts',
      'pages',
      'authors',
      'categories',
      'tags',
      'media',
      'users',
    ]

    for (const collection of collections) {
      try {
        console.log(`📦 Exporting ${collection}...`)
        const result = await payload.find({
          collection,
          limit: 1000, // Adjust if you have more records
          depth: 0, // Avoid deep population for export
        })

        exportData[collection] = result.docs
        console.log(`✅ Exported ${result.docs.length} ${collection} records`)
      } catch (error) {
        console.log(`⚠️  Could not export ${collection}:`, error.message)
        exportData[collection] = []
      }
    }

    // Save to file
    const exportPath = path.join(process.cwd(), 'data-export.json')
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2))

    console.log(`✅ Data exported to: ${exportPath}`)
    console.log('📊 Export summary:')

    Object.entries(exportData).forEach(([collection, data]) => {
      console.log(`   ${collection}: ${data.length} records`)
    })

    process.exit(0)
  } catch (error) {
    console.error('❌ Export failed:', error)
    process.exit(1)
  }
}

exportData()
