#!/usr/bin/env node

/**
 * Import data to new Vercel Postgres database after migration
 * Run with: node scripts/import-data.js
 */

import fs from 'fs'
import path from 'path'
import { config } from 'dotenv'

async function importData() {
  try {
    console.log('🔄 Starting data import to Vercel Postgres...')

    // Import Payload after environment is loaded
    config({ path: '.env.local' })

    const { getPayload } = await import('payload')
    const payloadConfig = await import('../src/payload.config.ts')

    const payload = await getPayload({ config: payloadConfig.default })

    // Read export file
    const exportPath = path.join(process.cwd(), 'data-export.json')
    if (!fs.existsSync(exportPath)) {
      console.error('❌ No data-export.json file found. Run export-data.js first.')
      process.exit(1)
    }

    const exportData = JSON.parse(fs.readFileSync(exportPath, 'utf8'))

    // Import in specific order to handle relationships
    const importOrder = [
      'users',
      'tenants',
      'authors',
      'categories',
      'tags',
      'media',
      'posts',
      'pages',
    ]

    for (const collection of importOrder) {
      if (!exportData[collection] || exportData[collection].length === 0) {
        console.log(`⏭️  Skipping ${collection} (no data)`)
        continue
      }

      try {
        console.log(`📦 Importing ${collection}...`)
        let imported = 0

        for (const doc of exportData[collection]) {
          try {
            // Remove Payload internal fields
            const cleanDoc = { ...doc }
            delete cleanDoc.id
            delete cleanDoc.createdAt
            delete cleanDoc.updatedAt

            await payload.create({
              collection,
              data: cleanDoc,
            })
            imported++
          } catch (error) {
            console.log(`⚠️  Failed to import ${collection} record:`, error.message)
          }
        }

        console.log(
          `✅ Imported ${imported}/${exportData[collection].length} ${collection} records`,
        )
      } catch (error) {
        console.log(`❌ Failed to import ${collection}:`, error.message)
      }
    }

    console.log('✅ Data import completed!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Import failed:', error)
    process.exit(1)
  }
}

importData()
